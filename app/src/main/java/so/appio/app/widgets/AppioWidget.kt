package so.appio.app.widgets

import android.content.Context
import android.util.Log
import androidx.compose.ui.unit.dp
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.provideContent
import androidx.glance.currentState
import androidx.glance.layout.Spacer
import androidx.glance.layout.padding
import kotlinx.serialization.json.Json
import so.appio.app.R
import so.appio.app.data.entity.widget.WidgetTemplate
import so.appio.app.ui.widgets.WidgetConfigError
import so.appio.app.ui.widgets.WidgetContent
import so.appio.app.ui.widgets.WidgetZeroStateContent
import so.appio.app.utils.SentryErrorHandler
import io.sentry.Sentry

class AppioWidget : GlanceAppWidget(errorUiLayout = R.layout.widget_error_layout) {

    // Use SizeMode.Exact for real-time size detection
    override val sizeMode = SizeMode.Exact

    companion object {
        private const val TAG = "LOG:AppioWidget"
        const val TAG_UI = "LOG:UI:AppioWidget"

        // Key to (force) trigger widget UI update
        val KEY_NONCE = stringPreferencesKey("timestamp")
        val KEY_SERVICE_ID = stringPreferencesKey("serviceId")
        val KEY_WIDGET_ID = stringPreferencesKey("widgetId")
        val KEY_WIDGET_CONFIG = stringPreferencesKey("widgetConfig")
    }

    // Custom error processing on failure. Already showing widget_error_layout
    override fun onCompositionError(
        context: Context,
        glanceId: GlanceId,
        appWidgetId: Int,
        throwable: Throwable
    ) {
        Log.e(TAG, "Widget composition error for appWidgetId: $appWidgetId", throwable)

        // Report widget composition error to Sentry
        SentryErrorHandler.reportWidgetError(
            exception = throwable,
            widgetId = appWidgetId,
            operation = "composition"
        )

        super.onCompositionError(context, glanceId, appWidgetId, throwable)
    }

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        Log.d(TAG, "Providing glance for widget id $id")

        // Note: this might not be needed
        val glanceAppWidgetManager = GlanceAppWidgetManager(context)
        val appWidgetId = try {
            glanceAppWidgetManager.getAppWidgetId(id)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app widget ID for glance id $id", e)
            0
        }

        Log.d(TAG, "Widget rendering - glanceId: $id, appWidgetId: $appWidgetId")

        // Only for UI composition and rendering cached data
        // Data fetching is in AppioWidgetUpdateWorker.doWork()
        provideContent {
            val nonce = currentState(KEY_NONCE) ?: ""
            val serviceId = currentState(KEY_SERVICE_ID)
            val widgetId = currentState(KEY_WIDGET_ID)
            val widgetConfig: String? = currentState(KEY_WIDGET_CONFIG)
            val isConfigured = serviceId != null && widgetId != null && widgetConfig != null &&
                    serviceId.isNotBlank() && widgetId.isNotBlank() && widgetConfig.isNotBlank()

            // Send widget config to Sentry for debugging
            Sentry.configureScope { scope ->
                scope.setExtra("widget.app_widget_id", appWidgetId.toString())
                scope.setExtra("widget.service_id", serviceId ?: "null")
                scope.setExtra("widget.widget_id", widgetId ?: "null")
                scope.setExtra("widget.is_configured", isConfigured.toString())
                scope.setExtra("widget.config", widgetConfig ?: "null")
            }

            Log.d(TAG, "Widget $appWidgetId state check:")
            Log.d(TAG, "  - nonce: $nonce")
            Log.d(TAG, "  - serviceId: $serviceId")
            Log.d(TAG, "  - widgetId: $widgetId")
            Log.d(TAG, "  - isConfigured: $isConfigured")
            // Log.d(TAG, "  - config: $widgetConfig")

            val template = widgetConfig?.takeIf { it.isNotBlank() }?.let {
                try {
                    Json.decodeFromString<WidgetTemplate>(it)
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing widget template JSON", e)

                    // Report widget template parsing error to Sentry
                    SentryErrorHandler.reportWidgetError(
                        exception = e,
                        widgetId = appWidgetId,
                        serviceId = serviceId,
                        operation = "template_parsing"
                    )

                    null
                }
            }

            GlanceTheme {
                if (isConfigured) {
                    if (template != null) {
                        WidgetContent(template = template, serviceId = serviceId)
                    } else {
                        WidgetConfigError()
                    }
                } else {
                    WidgetZeroStateContent(appWidgetId = appWidgetId)
                }
                // Hack to force UI refresh
                Spacer(modifier = GlanceModifier.padding(if (nonce.endsWith("x")) 0.1.dp else 0.2.dp))
            }
        }
    }
}
